# Testing Guide

This project uses <PERSON><PERSON> and <PERSON>act Testing Library for testing React components.

## Test Setup

### Configuration Files
- `jest.config.js` - Jest configuration with ES module handling and mocks
- `src/setupTests.ts` - Test environment setup with browser API mocks
- `src/test-utils.tsx` - Custom render function with Redux Provider

### Available Scripts
```bash
npm test          # Run all tests once
npm run test:watch # Run tests in watch mode
```

## Test Examples

### Basic Component Testing
See `src/shared/components/button/Button.test.tsx` for examples of:
- Rendering components
- Testing user interactions
- Testing props and state
- Testing loading and disabled states
- Testing accessibility features

### Advanced Testing Patterns
See `src/shared/components/example-advanced.test.tsx` for examples of:
- Testing multiple components together
- Using `userEvent` for realistic user interactions
- Testing async behavior with `waitFor`
- Testing accessibility features
- Error handling and edge cases

## Key Testing Utilities

### Custom Render Function
```typescript
import { render } from './test-utils'

// Automatically wraps components with Redux Provider
render(<MyComponent />)
```

### Common Testing Library Queries
```typescript
// By role (preferred)
screen.getByRole('button', { name: /submit/i })

// By label text
screen.getByLabelText(/email/i)

// By test id (when semantic queries aren't possible)
screen.getByTestId('custom-element')

// Multiple elements
screen.getAllByRole('button')
```

### User Interactions
```typescript
import userEvent from '@testing-library/user-event'

const user = userEvent.setup()
await user.click(button)
await user.type(input, 'text')
await user.keyboard('{Enter}')
```

### Async Testing
```typescript
import { waitFor } from '@testing-library/react'

await waitFor(() => {
  expect(mockFunction).toHaveBeenCalled()
})
```

## Mocking

### Library Mocks
Located in `__mocks__/` directory:
- `react-markdown.js` - Mocks react-markdown component
- `remark-gfm.js` - Mocks remark-gfm plugin
- `exifr.js` - Mocks exifr library
- `html2pdf.js` - Mocks html2pdf library

### Browser API Mocks
Configured in `src/setupTests.ts`:
- `window.scrollTo`
- `HTMLCanvasElement.getContext`
- `window.matchMedia`

## Best Practices

1. **Use semantic queries** - Prefer `getByRole`, `getByLabelText` over `getByTestId`
2. **Test user behavior** - Focus on what users can see and do
3. **Use userEvent** - More realistic than `fireEvent`
4. **Test accessibility** - Include aria-labels, roles, etc.
5. **Mock external dependencies** - Keep tests isolated and fast
6. **Use descriptive test names** - Clearly describe what is being tested

## Common Patterns

### Testing Forms
```typescript
test('submits form with valid data', async () => {
  const user = userEvent.setup()
  const onSubmit = jest.fn()
  
  render(<MyForm onSubmit={onSubmit} />)
  
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
  await user.click(screen.getByRole('button', { name: /submit/i }))
  
  expect(onSubmit).toHaveBeenCalledWith({ email: '<EMAIL>' })
})
```

### Testing Loading States
```typescript
test('shows loading state', () => {
  render(<Button isLoading>Save</Button>)
  
  const button = screen.getByRole('button')
  expect(button).toBeDisabled()
  expect(button).toHaveClass('loading')
})
```

### Testing Error States
```typescript
test('displays error message', async () => {
  const mockApi = jest.fn().mockRejectedValue(new Error('API Error'))
  
  render(<MyComponent api={mockApi} />)
  
  await user.click(screen.getByRole('button', { name: /submit/i }))
  
  await waitFor(() => {
    expect(screen.getByText(/error occurred/i)).toBeInTheDocument()
  })
})
```

## Troubleshooting

### Common Issues
1. **ES Module errors** - Add problematic modules to `transformIgnorePatterns` in `jest.config.js`
2. **Browser API not implemented** - Add mocks to `src/setupTests.ts`
3. **Redux context errors** - Use custom render from `test-utils.tsx`
4. **Async timing issues** - Use `waitFor` for async operations

### Debug Tips
- Use `screen.debug()` to see current DOM
- Use `screen.logTestingPlaygroundURL()` for query suggestions
- Check Jest configuration if modules aren't transforming properly
