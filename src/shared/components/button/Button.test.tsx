import { render, screen, fireEvent } from '../../../test-utils'
import Button from './Button'

describe('Button Component', () => {
  test('renders button with text', () => {
    render(<Button>Click me</Button>)

    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
  })

  test('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)

    const button = screen.getByRole('button', { name: /click me/i })
    fireEvent.click(button)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  test('can be disabled', () => {
    const handleClick = jest.fn()
    render(
      <Button onClick={handleClick} disabled>
        Disabled Button
      </Button>
    )

    const button = screen.getByRole('button', { name: /disabled button/i })
    expect(button).toBeDisabled()

    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  test('shows loading state', () => {
    render(<Button isLoading>Loading Button</Button>)

    const button = screen.getByRole('button', { name: /loading button/i })
    expect(button).toBeDisabled()
    expect(button).toHaveClass('loading')
  })

  test('applies custom className', () => {
    render(<Button className="custom-class">Styled Button</Button>)

    const button = screen.getByRole('button', { name: /styled button/i })
    expect(button).toHaveClass('custom-class')
  })

  test('supports different button types', () => {
    render(<Button type="submit">Submit Button</Button>)

    const button = screen.getByRole('button', { name: /submit button/i })
    expect(button).toHaveAttribute('type', 'submit')
  })

  test('renders with tooltip', () => {
    render(<Button tooltip="This is a helpful tooltip">Button with Tooltip</Button>)

    const button = screen.getByRole('button', { name: /button with tooltip/i })
    expect(button).toBeInTheDocument()

    // Check if tooltip content is in the document
    const tooltip = screen.getByText('This is a helpful tooltip')
    expect(tooltip).toBeInTheDocument()
  })

  test('disables button when loading', () => {
    const handleClick = jest.fn()
    render(
      <Button onClick={handleClick} isLoading>
        Loading Button
      </Button>
    )

    const button = screen.getByRole('button', { name: /loading button/i })
    expect(button).toBeDisabled()

    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })
})
