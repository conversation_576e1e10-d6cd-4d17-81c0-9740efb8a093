import { render, screen, fireEvent, waitFor } from '../../test-utils'
import userEvent from '@testing-library/user-event'
import Button from './button/Button'

// Example of testing multiple components together
const ButtonGroup = () => {
  return (
    <div role="group" aria-label="Action buttons">
      <Button type="button" className="primary">
        Save
      </Button>
      <Button type="button" className="secondary">
        Cancel
      </Button>
      <Button type="button" disabled>
        Disabled Action
      </Button>
    </div>
  )
}

describe('Advanced Testing Examples', () => {
  test('testing multiple buttons in a group', () => {
    render(<ButtonGroup />)

    // Find the button group
    const buttonGroup = screen.getByRole('group', { name: /action buttons/i })
    expect(buttonGroup).toBeInTheDocument()

    // Test individual buttons within the group
    const saveButton = screen.getByRole('button', { name: /save/i })
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    const disabledButton = screen.getByRole('button', { name: /disabled action/i })

    expect(saveButton).toBeEnabled()
    expect(saveButton).toHaveClass('primary')

    expect(cancelButton).toBeEnabled()
    expect(cancelButton).toHaveClass('secondary')

    expect(disabledButton).toBeDisabled()
  })

  test('user interactions with userEvent', async () => {
    const user = userEvent.setup()
    const handleClick = jest.fn()

    render(<Button onClick={handleClick}>Interactive Button</Button>)

    const button = screen.getByRole('button', { name: /interactive button/i })

    // Use userEvent for more realistic user interactions
    await user.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)

    // Test double click
    await user.dblClick(button)
    expect(handleClick).toHaveBeenCalledTimes(3) // 1 + 2 more clicks
  })

  test('testing with custom queries and matchers', () => {
    render(
      <div>
        <Button className="btn-primary" bgColor="#007bff">
          Primary Button
        </Button>
        <Button className="btn-secondary" bgColor="#6c757d">
          Secondary Button
        </Button>
      </div>
    )

    // Using getAllByRole to get multiple buttons
    const buttons = screen.getAllByRole('button')
    expect(buttons).toHaveLength(2)

    // Test specific button properties
    const primaryButton = screen.getByRole('button', { name: /primary button/i })
    const secondaryButton = screen.getByRole('button', { name: /secondary button/i })

    expect(primaryButton).toHaveClass('btn-primary')
    expect(secondaryButton).toHaveClass('btn-secondary')
  })

  test('testing async behavior with waitFor', async () => {
    const slowHandler = jest.fn().mockImplementation(() => {
      return new Promise((resolve) => setTimeout(resolve, 100))
    })

    render(<Button onClick={slowHandler}>Async Button</Button>)

    const button = screen.getByRole('button', { name: /async button/i })
    fireEvent.click(button)

    // Wait for async operation to complete
    await waitFor(() => {
      expect(slowHandler).toHaveBeenCalled()
    })
  })

  test('testing error boundaries and error states', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    // Test that button handles edge cases gracefully
    render(<Button {...({ type: 'invalid' } as any)}>Button with Invalid Props</Button>)

    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  test('testing accessibility features', () => {
    render(
      <Button aria-label="Close dialog" aria-describedby="close-help" title="Click to close the dialog">
        ×
      </Button>
    )

    const button = screen.getByRole('button', { name: /close dialog/i })
    expect(button).toHaveAttribute('aria-describedby', 'close-help')
    expect(button).toHaveAttribute('title', 'Click to close the dialog')
  })
})
