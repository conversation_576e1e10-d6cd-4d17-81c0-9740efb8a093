import React, { useEffect, useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../styles/styled'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { lostOpportunity, unlostOpportunity } from '../../logic/apis/sales'
import { getDataFromLocalStorage, isSuccess, notify, startOfDate } from '../../shared/helpers/util'
import Button from '../../shared/components/button/Button'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import { lostContactLead, unlostContactLead } from '../../logic/apis/contact'
import { StepModalContainer } from '../newLead/lostModal/styles'
import { CrossContainer, ModalHeader, ModalHeaderContainer } from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { IntendWidth } from '../contact/style'
import { SubOption } from './InvalidLeadModal'

interface I_LostModal {
  onClose: any
  onComplete: any
  isLost: boolean
  setIsLost: any
  // fetchActivity: () => void
  // initFetchContact: () => Promise<void>
  leadId: string
}

interface I_initialValues {
  reason: string
  date: string
  lostNote: string
}

const lostReasons: Record<string, { subOptions?: SubOption[] }> = {
  'Too Expensive': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Price Shopping': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Went With Other Provider': {
    subOptions: [{ label: 'Who did they go with?', field: 'notes' }],
  },
  'Discuss With Partner': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Wants to Wait': {
    subOptions: [{ label: "What's their timeframe?", field: 'notes' }],
  },
  'Ghosted/Unreachable': {
    subOptions: [{ label: 'How many times did you reach out?', field: 'notes' }],
  },
  Other: {
    subOptions: [{ label: 'Lost lead notes', field: 'notes' }],
  },
}

const LostLeadModal: React.FC<I_LostModal> = (props) => {
  const { onClose, onComplete, leadId, isLost } = props
  const initialValues: I_initialValues = {
    reason: '',
    date: new Date().toISOString(),
    lostNote: '',
  }

  const { contactId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)
  interface CheckpointDates {
    [key: string]: string
  }

  const lostSchema = Yup.object().shape({
    lostNote: !isLost ? Yup.string().required('Required').min(2, 'Too Short!') : Yup.string().required('Required'),
    reason: !isLost ? Yup.string().required('Required') : Yup.string().notRequired(),
  })

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)

    try {
      const response = await (!isLost
        ? lostContactLead(
            {
              reason: values?.reason,
              notes: values?.lostNote?.trim(),
              date: new Date().toISOString(),
              memberId: currentMember._id!,
            },
            leadId!
          )
        : unlostContactLead(
            {
              reason: values.lostNote?.trim(),
              date: new Date().toISOString(),
              memberId: currentMember._id!,
            },
            leadId!
          ))
      if (isSuccess(response)) {
        // initFetchContact()
        onComplete()
        // fetchActivity()
        notify(`${isLost ? 'Unlost' : 'Lost'} Lead!`, 'success')
        setLoading(false)
        // !isLost
        //   ? setOppData((pre: any) => ({ ...pre, lostDate: new Date(), lostReason: values.reason }))
        //   : setOppData((pre: any) => {
        //       const { lostDate, lostReason, ...newObj } = pre
        //       return newObj
        //     })
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log(err)
    } finally {
      onClose()
      setLoading(false)
    }
  }

  return (
    <StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={lostSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          useEffect(() => {
            if (values?.reason) {
              setFieldValue('lostNote', '')
            }
          }, [values?.reason])
          return (
            <>
              <ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <ModalHeader>{isLost ? 'Unlost' : 'Lost'} Lead</ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </CrossContainer>
              </ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    {!isLost ? (
                      <>
                        <SharedDateAndTime
                          value={values.date}
                          labelName="Date"
                          stateName="date"
                          setFieldValue={setFieldValue}
                          error={touched.date && errors.date ? true : false}
                        />

                        <CustomSelect
                          labelName="Lost Reason"
                          stateName="reason"
                          value={values?.reason || ''}
                          error={!!(touched?.reason && errors?.reason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={Object.keys(lostReasons)}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        {/* <IntendWidth>
                          {values?.reason === 'Other (Describe in notes)' && (
                            <InputWithValidation
                              labelName="Lost Reason Notes"
                              stateName="lostNote"
                              error={touched.lostNote && errors.lostNote ? true : false}
                            />
                          )}
                        </IntendWidth> */}

                        {values?.reason &&
                          lostReasons[values.reason as keyof typeof lostReasons]?.subOptions?.some(
                            (option: SubOption) => option.field === 'notes'
                          ) && (
                            <IntendWidth>
                              <InputWithValidation
                                labelName={
                                  lostReasons[values.reason as keyof typeof lostReasons]?.subOptions?.find(
                                    (option: SubOption) => option.field === 'notes'
                                  )?.label || 'Notes*'
                                }
                                stateName="lostNote"
                                error={touched.lostNote && errors.lostNote ? true : false}
                              />
                            </IntendWidth>
                          )}
                      </>
                    ) : (
                      <InputWithValidation
                        labelName="Lost Reason Notes"
                        stateName="lostNote"
                        error={touched.lostNote && errors.lostNote ? true : false}
                      />
                    )}

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" maxWidth="150px" isLoading={loading}>
                        Submit
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </StepModalContainer>
  )
}

export default LostLeadModal
