module.exports = {
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.(t|j)sx?$': 'babel-jest',
  },
  // Don't transform most node_modules, but allow some specific ones
  transformIgnorePatterns: ['node_modules/(?!(react-markdown)/)'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|webp|svg|otf|ttf|woff|woff2|eot)$': '<rootDir>/__mocks__/fileMock.js',
    '^react-markdown$': '<rootDir>/__mocks__/react-markdown.js',
    '^remark-gfm$': '<rootDir>/__mocks__/remark-gfm.js',
    '^exifr/dist/full.esm$': '<rootDir>/__mocks__/exifr.js',
    '^html2pdf.js$': '<rootDir>/__mocks__/html2pdf.js',
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
}
